module hbm3_mm_bfm #(
    parameter [9:0] HBM_ID = 0,
    parameter [4:0] CHANNEL_ID = 0,
    parameter [0:0] PC_ID = 0,
    parameter ADDR_WIDTH = 64,
    parameter DATA_WIDTH = 256,
    // NOTE: Must be a parameter as it's used in port declaration
    parameter DATA_BYTE_WIDTH = DATA_WIDTH/8,
    parameter FIFO_AWIDTH = 8,  // FIFO depth of 256
    parameter MAX_OUTSTANDING_READS = 16,
    parameter BLOCKING_READ_THRESHOLD = 0
)(
    // Write Port
    input  wire                           mm_clk,
    input  wire                           mm_rstn,
    input  wire                           mm_wen,
    input  wire [ADDR_WIDTH-1:0]         mm_waddr,
    input  wire [DATA_WIDTH-1:0]         mm_wdata,
    input  wire [DATA_BYTE_WIDTH-1:0]    mm_wmask,
    
    // Read Port
    input  wire                           mm_ren,
    input  wire [ADDR_WIDTH-1:0]         mm_raddr,
    input                                mm_rready,
    output reg  [DATA_WIDTH-1:0]         mm_rdata,
    output reg                           mm_rvalid,

    input wire [31:0]                    read_latency
);

localparam FIFO_DEPTH = (1 << FIFO_AWIDTH);
localparam OUTSTANDING_READS_AWIDTH = $clog2(MAX_OUTSTANDING_READS);

localparam [15:0] BFM_ID = {HBM_ID, CHANNEL_ID, PC_ID};

`include "hbm3_mm_bfm_remap.svh"


`ifdef MM_HBM3_SINGLE_STORAGE_NONBLOCKING
    // FIFO definitions
    reg [DATA_WIDTH-1:0] read_fifo [0:FIFO_DEPTH-1];
    bit [FIFO_AWIDTH:0] fifo_rptr;
    bit [FIFO_AWIDTH:0] fifo_wptr;
    bit [FIFO_AWIDTH:0] left_space;

    // FIFO status
    wire fifo_empty;
    wire fifo_full;

    // Data and address remap
    reg [64-1:0] remapped_raddr;
    reg [64-1:0] remapped_waddr;
    reg [DATA_WIDTH-1:0] remapped_wdata;
    reg [DATA_BYTE_WIDTH-1:0] remapped_wmask;

    import "DPI-C" context function void h2s_hbm3_init_bfm(
        input bit [15:0] bfm_id,
        input int unsigned DATA_WIDTH
    );

    // Import DPI-C functions
    //(* is_nonblocking_dpi = true *)
    import "DPI-C" context function void h2s_hbm3_write(
        input bit [15:0] bfm_id,
        input bit [64-1:0] addr,
        input bit [DATA_WIDTH-1:0] data,
        input int unsigned data_size,
        input bit [DATA_BYTE_WIDTH-1:0] mask
    );

   import "DPI-C" context function void h2s_hbm3_read(
       input bit [15:0] bfm_id,
       input bit [63:0] addr,
       output bit [DATA_WIDTH-1:0] data
   );

    //(* is_nonblocking_dpi = true *)
    import "DPI-C" context function void h2s_hbm3_read_request(
        input bit [15:0] bfm_id,
        input bit [64-1:0] addr,
        input int unsigned data_size
    );

    // Export callback function
    export "DPI-C" function s2h_hbm3_read_data_reply;

    // Import new DPI-C function for requesting data
    //(* is_nonblocking_dpi = true *)
    import "DPI-C" context function void h2s_hbm3_read_data_request(
        input bit [15:0] bfm_id,
        input bit [15:0] left_space
    );

    import "DPI-C" context function void h2s_hbm3_read_data_blocking(
        input bit [15:0] bfm_id
    );

    // Read transaction tracking (synthesizable)
    reg [31:0] cycle_counter;
    reg [31:0] pending_read_start_cycle[0:MAX_OUTSTANDING_READS-1];
    reg [OUTSTANDING_READS_AWIDTH:0] pending_req_count;
    reg [OUTSTANDING_READS_AWIDTH:0] pending_resp_count;
    wire [OUTSTANDING_READS_AWIDTH:0] num_pending_reads = pending_req_count - pending_resp_count;

    // Registers for the dedicated low-latency read pipeline
    reg [DATA_WIDTH-1:0] ll_read_data;
    reg ll_read_valid;
    reg [DATA_WIDTH-1:0] ll_rdata_hold;
    reg ll_rvalid_hold;


    // Counters for request_data tracking
    reg [FIFO_AWIDTH:0] request_data_send_count;    // Number of request_data sent
    reg [FIFO_AWIDTH:0] request_data_resp_count;    // Number of responses received

    // Counters for read requests and responses
    reg [15:0] read_req_count;   // Number of read requests sent
    reg [15:0] read_resp_count;  // Number of read responses received

`pragma protect begin

    // DPI-C callback function - Write to FIFO
    // This is now the ONLY place that modifies FIFO pointers and data
    function void s2h_hbm3_read_data_reply(input bit [DATA_WIDTH-1:0] data, input byte valid);
    begin
        // Always increment request_data_resp_count as this function is called
        // for every request_data (both from h2s_hbm3_read_data_request and h2s_hbm3_read_data_blocking)
        request_data_resp_count = request_data_resp_count + 1;

        if (valid) begin
            if (num_pending_reads > 0) begin
                if (!fifo_full) begin
                    read_fifo[fifo_wptr[FIFO_AWIDTH-1:0]] = data;
                    fifo_wptr = fifo_wptr + 1;
                end
                // A read completed, increment the response counter
                pending_resp_count = pending_resp_count + 1;
            end
        end
    end
    endfunction
    
    // Calculate FIFO available space
    function automatic [FIFO_AWIDTH:0] calculate_space(
        bit [FIFO_AWIDTH:0] rptr, 
        bit [FIFO_AWIDTH:0] wptr
    );
        bit [FIFO_AWIDTH-1:0] left;
    begin
        if ((wptr[FIFO_AWIDTH] == ~rptr[FIFO_AWIDTH]) && 
            (rptr[FIFO_AWIDTH-1:0] >= wptr[FIFO_AWIDTH-1:0])) begin
            calculate_space = rptr[FIFO_AWIDTH-1:0] - wptr[FIFO_AWIDTH-1:0];
        end else begin
            calculate_space = FIFO_DEPTH - wptr[FIFO_AWIDTH-1:0] + rptr[FIFO_AWIDTH-1:0];
        end
    end
    endfunction

    // Calculate pending request_data count
    function automatic [FIFO_AWIDTH:0] calculate_pending_request_data_count(
        bit [FIFO_AWIDTH:0] send_count,
        bit [FIFO_AWIDTH:0] resp_count
    );
    begin
        if (send_count[FIFO_AWIDTH] == ~resp_count[FIFO_AWIDTH]) begin
            calculate_pending_request_data_count = FIFO_DEPTH + send_count[FIFO_AWIDTH-1:0] - resp_count[FIFO_AWIDTH-1:0];
        end else begin
            calculate_pending_request_data_count = send_count[FIFO_AWIDTH-1:0] - resp_count[FIFO_AWIDTH-1:0];
        end
    end
    endfunction

    // FIFO status calculation
    assign fifo_empty = (fifo_rptr == fifo_wptr);
    assign fifo_full = (fifo_rptr[FIFO_AWIDTH] == ~fifo_wptr[FIFO_AWIDTH]) && 
                      (fifo_rptr[FIFO_AWIDTH-1:0] == fifo_wptr[FIFO_AWIDTH-1:0]);

    // Calculate available space
    always @(*) begin
        left_space = calculate_space(fifo_rptr, fifo_wptr);
    end

    // FIFO read and output control
    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            fifo_rptr <= '0;
            mm_rvalid <= 1'b0;
            mm_rdata <= '0;
            ll_rvalid_hold <= 1'b0;
        end else begin
            // Stage 2 of the low-latency pipeline
            ll_rdata_hold <= ll_read_data;
            ll_rvalid_hold <= ll_read_valid;

            // Default to not valid
            mm_rvalid <= 1'b0;

            // Output Mux: Low-latency path has priority
            // Only output data when mm_rready is asserted
            if (ll_rvalid_hold && mm_rready) begin
                mm_rdata <= ll_rdata_hold;
                mm_rvalid <= 1'b1;
            end else if (!fifo_empty && mm_rready) begin
                // Non-blocking FIFO path is active
                mm_rdata <= read_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
                mm_rvalid <= 1'b1;
                fifo_rptr <= fifo_rptr + 1; // Only advance FIFO when its data is used
            end
        end
    end

    // Write logic
    always @(posedge mm_clk) begin
        if (mm_wen && mm_rstn) begin
            $display("Write request: addr=%h, data=%h, mask=%h", mm_waddr, mm_wdata, mm_wmask);
            remapped_waddr = remap_addr(mm_waddr, HBM_ID, CHANNEL_ID, PC_ID);
            remapped_wdata = remap_data(mm_wdata, HBM_ID, CHANNEL_ID, PC_ID);
            remapped_wmask = remap_wmask(mm_wmask, HBM_ID, CHANNEL_ID, PC_ID);
            h2s_hbm3_write(BFM_ID, remapped_waddr, remapped_wdata, 
                        DATA_WIDTH, remapped_wmask);
        end
    end

    // Read request logic
    always @(posedge mm_clk) begin
        // Stage 1 of the low-latency pipeline
        ll_read_valid <= 1'b0; // Default to not valid

        if (mm_ren && mm_rstn) begin
            remapped_raddr = remap_addr(mm_raddr, HBM_ID, CHANNEL_ID, PC_ID);
            // If threshold is enabled and latency is low, use blocking read
            if (BLOCKING_READ_THRESHOLD > 0 && read_latency < BLOCKING_READ_THRESHOLD) begin
                $display("[SV] Low-latency read request: addr=%h, cycle=%d. Using blocking call.", mm_raddr, cycle_counter);
                h2s_hbm3_read(BFM_ID, remapped_raddr, ll_read_data);
                ll_read_valid <= 1'b1;
            end else begin // Otherwise, use non-blocking read
                if (num_pending_reads < MAX_OUTSTANDING_READS) begin
                    $display("[SV] Read request: addr=%h, cycle=%d", mm_raddr, cycle_counter);
                    
                    // Store the start cycle for this transaction, using req_count as the pointer
                    pending_read_start_cycle[pending_req_count[OUTSTANDING_READS_AWIDTH-1:0]] = cycle_counter;
                    pending_req_count = pending_req_count + 1;
                    
                    h2s_hbm3_read_request(BFM_ID, remapped_raddr, DATA_WIDTH);
                end else begin
                    $display("[SV] WARNING: Cannot issue read request. Maximum outstanding reads reached.");
                end
            end
        end
    end


    // Calculate pending request_data count and available space
    wire [FIFO_AWIDTH:0] pending_request_data_count = calculate_pending_request_data_count(request_data_send_count, request_data_resp_count);
    wire [FIFO_AWIDTH:0] available_space = (left_space > pending_request_data_count) ? 
                                          (left_space - pending_request_data_count) : '0;
    wire can_request_data = (available_space > 1);

    // Request data control logic
    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            read_req_count <= '0;
            read_resp_count <= '0;
            request_data_send_count <= request_data_resp_count;
            cycle_counter <= 0;
            pending_req_count <= 0;
            pending_resp_count <= 0;
        end else begin
            cycle_counter <= cycle_counter + 1;

            // Determine if we need to increment request_data_send_count
            // This ensures only one increment per clock cycle for synthesizability
            reg timeout_detected;
            reg normal_request_needed;

            timeout_detected = (num_pending_reads > 0) &&
                              (cycle_counter - pending_read_start_cycle[pending_resp_count[OUTSTANDING_READS_AWIDTH-1:0]] >= read_latency);

            normal_request_needed = (read_req_count != read_resp_count) && can_request_data && !timeout_detected;

            // Timeout logic has priority over normal request
            if (timeout_detected) begin
                $display("[SV] Timeout detected for oldest read request! Issuing blocking call at cycle %d.", cycle_counter);

                // Call blocking function which will internally call s2h_hbm3_read_data_reply
                // This ensures s2h_hbm3_read_data_reply is the only place that modifies FIFO
                h2s_hbm3_read_data_blocking(BFM_ID);

                // Increment request_data_send_count for timeout case
                request_data_send_count <= request_data_send_count + 1;
            end else if (normal_request_needed) begin
                // Request data if there are pending read requests and available space
                h2s_hbm3_read_data_request(BFM_ID, left_space);
                request_data_send_count <= request_data_send_count + 1;
            end

            // Increment count when sending new read request
            if (mm_ren) begin
                read_req_count <= read_req_count + 1;
            end

            // Increment count when receiving read response
            if (mm_rvalid) begin
                read_resp_count <= read_resp_count + 1;
            end
        end
    end


    // Initialization
    initial begin
        fifo_rptr = '0;
        fifo_wptr = '0;
        read_req_count = '0;
        read_resp_count = '0;
        request_data_send_count = '0;
        request_data_resp_count = '0;
        left_space = FIFO_DEPTH;
        cycle_counter = 0;
        pending_req_count = 0;
        pending_resp_count = 0;
        h2s_hbm3_init_bfm(BFM_ID, DATA_WIDTH);
    end

`pragma protect end

`else // BLOCKING

    // read data buffer
    reg [DATA_WIDTH-1:0] read_data;
    reg [DATA_WIDTH-1:0] read_data_hold;  // hold last read data
    reg read_valid;
    // Data and address remap
    reg [63:0] remapped_raddr;
    reg [63:0] remapped_waddr;
    reg [DATA_WIDTH-1:0] remapped_wdata;
    reg [DATA_BYTE_WIDTH-1:0] remapped_wmask;

    // Import DPI-C functions
    import "DPI-C" context function void h2s_hbm3_write(
        input bit [15:0] bfm_id,
        input bit [63:0] addr,
        input bit [DATA_WIDTH-1:0] data,
        input int unsigned data_size,
        input bit [DATA_BYTE_WIDTH-1:0] mask
    );

    import "DPI-C" context function void h2s_hbm3_read(
        input bit [15:0] bfm_id,
        input bit [63:0] addr,
        output bit [DATA_WIDTH-1:0] data
    );

    import "DPI-C" context function void h2s_hbm3_init_bfm(
        input bit [15:0] bfm_id,
        input int unsigned DATA_WIDTH
    );

`pragma protect begin

    // Write logic
    always @(posedge mm_clk) begin
        if (mm_wen && mm_rstn) begin
            remapped_waddr = remap_addr(mm_waddr, HBM_ID, CHANNEL_ID, PC_ID);
            remapped_wdata = remap_data(mm_wdata, HBM_ID, CHANNEL_ID, PC_ID);
            remapped_wmask = remap_wmask(mm_wmask, HBM_ID, CHANNEL_ID, PC_ID);
            h2s_hbm3_write(BFM_ID, remapped_waddr, remapped_wdata,
                        DATA_WIDTH, remapped_wmask);
        end

    end

    // Read request and data handling
    always @(posedge mm_clk) begin
        if (!mm_rstn) begin
            mm_rvalid <= 1'b0;
            mm_rdata <= '0;
            read_valid <= 1'b0;
            read_data <= '0;
            read_data_hold <= '0;
        end else begin
            if (mm_ren) begin
                remapped_raddr = remap_addr(mm_raddr, HBM_ID, CHANNEL_ID, PC_ID);
                h2s_hbm3_read(BFM_ID, remapped_raddr, read_data);
                read_valid <= 1'b1;
                read_data_hold <= read_data;  // save current read data
            end else begin
                read_valid <= 1'b0;
            end

            // 输出控制 - 只有当mm_rready有效时才输出读数据
            mm_rvalid <= read_valid && mm_rready;
            if (read_valid && mm_rready) begin
                mm_rdata <= read_data_hold;  // use hold data
            end
        end
    end

    initial begin
        h2s_hbm3_init_bfm(BFM_ID, DATA_WIDTH);
    end

`pragma protect end

`endif

endmodule
