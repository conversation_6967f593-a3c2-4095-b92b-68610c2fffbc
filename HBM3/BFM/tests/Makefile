# Makefile for HBM3 BFM simulation
# Supports VCS and Xcelium, with separate test suites.

# --- Test Suite Selection ---
# Use 'make TEST_SUITE=blocking' or 'make TEST_SUITE=nonblocking'. Default is nonblocking.
TEST_SUITE ?= nonblocking

# Set BFM compile-time defines based on the test suite
BFM_MODE_DEFINE =
ifeq ($(TEST_SUITE), nonblocking)
	BFM_MODE_DEFINE = +define+MM_HBM3_SINGLE_STORAGE_NONBLOCKING
endif

# General Settings
CXX = g++
INCLUDES_PATH = $(TEST_SUITE)/src/cpp
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -g -I$(INCLUDES_PATH)

# Source Files
RTL_SOURCES = ../src/hbm3_mm_bfm.sv
TB_SOURCES = $(TEST_SUITE)/src/sv/hbm3_mm_tb.sv
ALL_SV_SOURCES = $(RTL_SOURCES) $(TB_SOURCES)

DPI_CPP_SOURCES = $(TEST_SUITE)/src/cpp/fake_hbm3_mm_bfm.cc
SVLIB_NAME = $(TEST_SUITE)/fake_hbm3_mm_bfm_$(TEST_SUITE).so

# Output Directories
SIM_DIR = $(TEST_SUITE)/sim
VCS_DIR = $(SIM_DIR)/vcs
XLM_DIR = $(SIM_DIR)/xlm

# VCS/Xcelium Settings
VCS_HOME ?= $(shell which vcs 2>/dev/null | sed 's|/bin/vcs||')
XRUN_HOME ?= $(shell which xrun 2>/dev/null | sed 's|/bin/xrun||')

VCS_INCLUDE = $(VCS_HOME)/include
SHARED_FLAGS = -fPIC -shared -g -lpthread

# Debug options
VCD = 0
ifeq ($(VCD), 1)
	DUMP_VCD = +define+DUMP_VCD
else
	DUMP_VCD =
endif

DEBUG_HW = 1
ifeq ($(DEBUG_HW), 1)
	DEBUG_HW_FLAG = +define+DEBUG_BFM
else
	DEBUG_HW_FLAG =
endif

HW_DEFINE_OPTS = $(DUMP_VCD) $(DEBUG_HW_FLAG)

# Targets
.PHONY: all clean vcs xlm vcs_comp vcs_run vcs_grun xlm_comp xlm_run xlm_grun cleanall create_dirs help

all: help

# Create simulation directories
create_dirs:
	@echo "Creating simulation directories..."
	@mkdir -p $(VCS_DIR) $(XLM_DIR)

# DPI library compilation
$(SVLIB_NAME): $(DPI_CPP_SOURCES)
	@echo "Building DPI shared library for $(TEST_SUITE): $(SVLIB_NAME)..."
	$(CXX) $(CXXFLAGS) $(SHARED_FLAGS) -I$(VCS_INCLUDE) $(DPI_CPP_SOURCES) -o $(SVLIB_NAME)

# VCS targets
vcs: vcs_run

vcs_comp: create_dirs $(SVLIB_NAME)
	@echo "Compiling for VCS ($(TEST_SUITE))..."
	cd $(VCS_DIR) && \
	vcs -full64 -sverilog -debug_access+all -timescale=1ns/1ps \
		-kdb -lca +vpi \
		$(HW_DEFINE_OPTS) \
		$(BFM_MODE_DEFINE) \
		+incdir+../../../src/sv \
		+incdir+../../../../src \
		../../../$(RTL_SOURCES) \
		../../../$(TB_SOURCES) \
		-top hbm3_mm_tb \
		-o simv \
		-l vcs_comp.log \
		-CFLAGS "-g -I../../../$(INCLUDES_PATH)"

vcs_run: vcs_comp
	@echo "Running VCS simulation ($(TEST_SUITE))..."
	cd $(VCS_DIR) && \
	./simv -sv_lib ../../../$(basename $(SVLIB_NAME)) \
		-ucli -do ../../../scripts/vsim.tcl \
		-l vcs.log

vcs_grun: vcs_comp
	@echo "Running VCS simulation with GUI ($(TEST_SUITE))..."
	cd $(VCS_DIR) && \
	./simv -gui -sv_lib ../../../$(basename $(SVLIB_NAME)) \
		-ucli -do ../../../scripts/vsim.tcl \
		-l vcs_gui.log

# Xcelium targets
xlm: xlm_run

xlm_comp: create_dirs $(SVLIB_NAME)
	@echo "Compiling for Xcelium ($(TEST_SUITE))..."
	cd $(XLM_DIR) && \
	xrun -c -sv -access +rwc -linedebug \
		$(HW_DEFINE_OPTS) \
		$(BFM_MODE_DEFINE) \
		+incdir+../../../src/sv \
		+incdir+../../../../src \
		../../../$(RTL_SOURCES) \
		../../../$(TB_SOURCES) \
		-top hbm3_mm_tb \
		-status -log xrun_compile.log

xlm_run: xlm_comp
	@echo "Running Xcelium simulation ($(TEST_SUITE))..."
	cd $(XLM_DIR) && \
	xrun -R -sv_lib ../../../$(SVLIB_NAME) \
		-input ../../../scripts/vsim.tcl \
		-status -l xrun_sim.log

xlm_grun: xlm_comp
	@echo "Running Xcelium simulation with GUI ($(TEST_SUITE))..."
	cd $(XLM_DIR) && \
	xrun -R -gui -sv_lib ../../../$(SVLIB_NAME) \
		-input ../../../scripts/vsim.tcl \
		-status -l xrun_gui.log

# Clean targets
clean:
	@echo "Cleaning build artifacts for $(TEST_SUITE)..."
	rm -rf $(SVLIB_NAME)
	rm -rf $(SIM_DIR)

cleanall:
	@echo "Cleaning all generated files..."
	rm -rf blocking/sim blocking/*.so nonblocking/sim nonblocking/*.so

# Help target
help:
	@echo "HBM3 BFM Makefile"
	@echo ""
	@echo "Usage: make [target] [TEST_SUITE=nonblocking|blocking]"
	@echo "Default TEST_SUITE is 'nonblocking'."
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Show this help message (default)"
	@echo "  $(SVLIB_NAME)    - Build the DPI-C shared library"
	@echo ""
	@echo "VCS Targets:"
	@echo "  vcs          - Compile and run simulation with VCS"
	@echo "  vcs_comp     - Compile design with VCS"
	@echo "  vcs_run      - Run simulation with VCS"
	@echo "  vcs_grun     - Run simulation with VCS and GUI"
	@echo ""
	@echo "Xcelium Targets:"
	@echo "  xlm          - Compile and run simulation with Xcelium"
	@echo "  xlm_comp     - Compile design with Xcelium"
	@echo "  xlm_run      - Run simulation with Xcelium"
	@echo "  xlm_grun     - Run simulation with Xcelium and GUI"
	@echo ""
	@echo "Management Targets:"
	@echo "  create_dirs  - Create simulation directories (sim/vcs, sim/xlm)"
	@echo "  clean        - Remove build artifacts"
	@echo "  cleanall     - Remove all generated files including sim directories"
	@echo "  help         - Show this help message"
