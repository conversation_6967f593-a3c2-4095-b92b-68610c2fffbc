// Test for mm_rready functionality in hbm3_mm_bfm
// This test verifies that read data is only returned when mm_rready is asserted

module mm_rready_test;
    parameter ADDR_WIDTH = 29;
    parameter DATA_WIDTH = 256;
    parameter DATA_BYTE_WIDTH = DATA_WIDTH/8;
    parameter CLK_PERIOD = 10;

    // Common signals
    logic mm_clk;
    logic mm_rstn;

    // BFM signals
    logic                           mm_wen;
    logic [ADDR_WIDTH-1:0]         mm_waddr;
    logic [DATA_WIDTH-1:0]         mm_wdata;
    logic [DATA_BYTE_WIDTH-1:0]    mm_wmask;
    
    logic                           mm_ren;
    logic [ADDR_WIDTH-1:0]         mm_raddr;
    logic                           mm_rready;
    logic [DATA_WIDTH-1:0]         mm_rdata;
    logic                           mm_rvalid;

    // Clock generation
    initial begin
        mm_clk = 0;
        forever #(CLK_PERIOD/2) mm_clk = ~mm_clk;
    end

    // Reset and signal initialization
    initial begin
        mm_rstn = 0;
        mm_wen = 0;
        mm_ren = 0;
        mm_rready = 0;  // 初始设置为低电平
        mm_waddr = 0;
        mm_wdata = 0;
        mm_wmask = 0;
        mm_raddr = 0;
        
        #(CLK_PERIOD * 10);
        mm_rstn = 1;
        #(CLK_PERIOD * 5);
        
        // Test sequence
        test_mm_rready_control();
        
        #(CLK_PERIOD * 20);
        $display("Test completed");
        $finish;
    end

    // Instantiate BFM for blocking mode test
    hbm3_mm_bfm #(
        .HBM_ID(0),
        .CHANNEL_ID(0),
        .PC_ID(0),
        .ADDR_WIDTH(ADDR_WIDTH),
        .DATA_WIDTH(DATA_WIDTH),
        .DATA_BYTE_WIDTH(DATA_BYTE_WIDTH)
    ) dut (
        .mm_clk(mm_clk),
        .mm_rstn(mm_rstn),
        .mm_wen(mm_wen),
        .mm_waddr(mm_waddr),
        .mm_wdata(mm_wdata),
        .mm_wmask(mm_wmask),
        .mm_ren(mm_ren),
        .mm_raddr(mm_raddr),
        .mm_rready(mm_rready),
        .mm_rdata(mm_rdata),
        .mm_rvalid(mm_rvalid),
        .read_latency(32'd100)
    );

    // Test task
    task test_mm_rready_control();
        logic [DATA_WIDTH-1:0] test_data = 256'hDEADBEEF_CAFEBABE_12345678_9ABCDEF0_FEDCBA09_87654321_ABCDEF01_23456789;
        
        $display("=== Testing mm_rready control functionality ===");
        
        // Step 1: Write test data
        $display("Step 1: Writing test data to address 0x1000");
        @(posedge mm_clk);
        mm_wen <= 1'b1;
        mm_waddr <= 29'h1000;
        mm_wdata <= test_data;
        mm_wmask <= {DATA_BYTE_WIDTH{1'b1}};  // Full mask
        @(posedge mm_clk);
        mm_wen <= 1'b0;
        
        // Step 2: Issue read request with mm_rready = 0
        $display("Step 2: Issuing read request with mm_rready = 0");
        @(posedge mm_clk);
        mm_ren <= 1'b1;
        mm_raddr <= 29'h1000;
        mm_rready <= 1'b0;  // Keep mm_rready low
        @(posedge mm_clk);
        mm_ren <= 1'b0;
        
        // Wait a few cycles and check that mm_rvalid remains low
        repeat(5) @(posedge mm_clk);
        if (mm_rvalid) begin
            $display("ERROR: mm_rvalid should not be asserted when mm_rready = 0");
        end else begin
            $display("PASS: mm_rvalid correctly remains low when mm_rready = 0");
        end
        
        // Step 3: Assert mm_rready and check that data is returned
        $display("Step 3: Asserting mm_rready and expecting data return");
        @(posedge mm_clk);
        mm_rready <= 1'b1;  // Assert mm_rready
        
        // Wait for mm_rvalid to be asserted
        fork
            begin
                repeat(10) @(posedge mm_clk);
                if (!mm_rvalid) begin
                    $display("ERROR: mm_rvalid was not asserted within 10 cycles after mm_rready = 1");
                end
            end
            begin
                wait(mm_rvalid);
                $display("PASS: mm_rvalid asserted when mm_rready = 1");
                if (mm_rdata == test_data) begin
                    $display("PASS: Read data matches written data");
                end else begin
                    $display("ERROR: Read data mismatch. Expected: %h, Got: %h", test_data, mm_rdata);
                end
            end
        join_any
        disable fork;
        
        // Step 4: Test multiple read cycles with mm_rready control
        $display("Step 4: Testing multiple read cycles with mm_rready control");
        @(posedge mm_clk);
        mm_rready <= 1'b0;  // Deassert mm_rready
        
        // Issue another read
        @(posedge mm_clk);
        mm_ren <= 1'b1;
        mm_raddr <= 29'h1000;
        @(posedge mm_clk);
        mm_ren <= 1'b0;
        
        // Wait and verify no data is returned
        repeat(3) @(posedge mm_clk);
        if (mm_rvalid) begin
            $display("ERROR: mm_rvalid should not be asserted when mm_rready = 0 (second test)");
        end else begin
            $display("PASS: mm_rvalid correctly remains low when mm_rready = 0 (second test)");
        end
        
        // Assert mm_rready again
        @(posedge mm_clk);
        mm_rready <= 1'b1;
        
        // Wait for data
        wait(mm_rvalid);
        $display("PASS: Second read completed successfully with mm_rready control");
        
        @(posedge mm_clk);
        mm_rready <= 1'b0;  // Clean up
        
    endtask

endmodule
