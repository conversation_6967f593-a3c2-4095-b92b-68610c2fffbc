module hbm3_mm_tb;
    parameter NUM_PCS = 8;
    parameter ADDR_WIDTH = 29;
    parameter DATA_WIDTH = 256;
    parameter DATA_BYTE_WIDTH = DATA_WIDTH/8;
    parameter CLK_PERIOD = 10;  // 100MHz for simulation

    // Common signals
    logic mm_clk;
    logic mm_rstn;

    // Arrays for PC signals
    logic [NUM_PCS-1:0]                    mm_wen;
    logic [NUM_PCS-1:0][ADDR_WIDTH-1:0]    mm_waddr;
    logic [NUM_PCS-1:0][DATA_WIDTH-1:0]    mm_wdata;
    logic [NUM_PCS-1:0][DATA_BYTE_WIDTH-1:0] mm_wmask;
    
    logic [NUM_PCS-1:0]                    mm_ren;
    logic [NUM_PCS-1:0][ADDR_WIDTH-1:0]    mm_raddr;
    logic [NUM_PCS-1:0][DATA_WIDTH-1:0]    mm_rdata;
    logic [NUM_PCS-1:0]                    mm_rvalid;

    // Clock generation
    initial begin
        mm_clk = 0;
        forever #(CLK_PERIOD/2) mm_clk = ~mm_clk;
    end

    // Reset generation
    initial begin
        mm_rstn = 0;
        #(CLK_PERIOD * 10);
        mm_rstn = 1;
    end

    // Generate 32 PC instances
    generate
        for (genvar i = 0; i < NUM_PCS; i++) begin : pc_inst
            hbm3_mm_bfm #(
                .HBM_ID(0),
                .CHANNEL_ID(i/2),      // 每个channel有2个PC
                .PC_ID(i%2),
                .ADDR_WIDTH(ADDR_WIDTH),
                .DATA_WIDTH(DATA_WIDTH),
                .DATA_BYTE_WIDTH(DATA_BYTE_WIDTH)
            ) pc_bfm (
                .mm_clk(mm_clk),
                .mm_rstn(mm_rstn),
                .mm_wen(mm_wen[i]),
                .mm_waddr(mm_waddr[i]),
                .mm_wdata(mm_wdata[i]),
                .mm_wmask(mm_wmask[i]),
                .mm_ren(mm_ren[i]),
                .mm_raddr(mm_raddr[i]),
                .mm_rdata(mm_rdata[i]),
                .mm_rvalid(mm_rvalid[i])
            );
        end
    endgenerate

    class Scoreboard;
        // Use associative array with {channel_id, pc_id} as key
        protected bit [DATA_WIDTH-1:0] expected_data[int][int][logic [ADDR_WIDTH-1:0]];
        protected int error_count;
        protected int check_count;

        function new();
            error_count = 0;
            check_count = 0;
        endfunction

        // 记录写操作
        function void record_write(
            input int channel_id,
            input int pc_id,
            input logic [ADDR_WIDTH-1:0] addr,
            input logic [DATA_WIDTH-1:0] data,
            input logic [DATA_BYTE_WIDTH-1:0] mask
        );
            logic [DATA_WIDTH-1:0] current_data;
            
            // 获取当前地址的数据
            if (expected_data[channel_id][pc_id].exists(addr)) begin
                current_data = expected_data[channel_id][pc_id][addr];
            end else begin
                current_data = '0;
            end

            // 按字节更新数据
            for (int i = 0; i < DATA_BYTE_WIDTH; i++) begin
                if (mask[i]) begin
                    current_data[i*8 +: 8] = data[i*8 +: 8];
                end
            end

            expected_data[channel_id][pc_id][addr] = current_data;
            $display("Channel[%0d] PC[%0d] Write recorded: Addr=%h, Data=%h, Mask=%h", 
                    channel_id, pc_id, addr, data, mask);
        endfunction

        // 检查读取的数据
        function void check_read(
            input int channel_id,
            input int pc_id,
            input logic [ADDR_WIDTH-1:0] addr,
            input logic [DATA_WIDTH-1:0] actual_data
        );
            check_count++;
            
            if (!expected_data[channel_id][pc_id].exists(addr)) begin
                $error("Channel[%0d] PC[%0d] Read from unwritten address %h, got %h", 
                      channel_id, pc_id, addr, actual_data);
                error_count++;
                return;
            end

            if (expected_data[channel_id][pc_id][addr] !== actual_data) begin
                $error("Channel[%0d] PC[%0d] Read data mismatch at addr %h: Expected=%h, Got=%h",
                      channel_id, pc_id, addr, expected_data[channel_id][pc_id][addr], actual_data);
                error_count++;
            end else begin
                $display("Channel[%0d] PC[%0d] Read check passed: Addr=%h, Data=%h",
                        channel_id, pc_id, addr, actual_data);
            end
        endfunction

        // 报告结果
        function void report();
            $display("\nScoreboard Report:");
            $display("Total Checks: %0d", check_count);
            $display("Total Errors: %0d", error_count);
            if (error_count == 0) begin
                $display("TEST PASSED!");
            end else begin
                $display("TEST FAILED!");
            end
        endfunction
    endclass

    // 创建记分牌实例
    Scoreboard scoreboard;

        task automatic single_pc_write(
        input int index, // index = 0~31
        input logic [ADDR_WIDTH-1:0] addr,
        input logic [DATA_WIDTH-1:0] data,
        input logic [DATA_BYTE_WIDTH-1:0] mask
    );
        int channel_id = index / 2;
        int pc_id = index % 2;
        mm_wen[index] <= 1'b1;
        mm_waddr[index] <= addr;
        mm_wdata[index] <= data;
        mm_wmask[index] <= mask;
        @(posedge mm_clk);
        mm_wen[index] <= 1'b0;
        
        // 记录写操作
        scoreboard.record_write(channel_id, pc_id, addr, data, mask);
    endtask

    // 改进的读任务
    task automatic single_pc_read(
        input int index,
        input logic [ADDR_WIDTH-1:0] addr
    );
        int channel_id = index / 2;
        int pc_id = index % 2;
        mm_ren[index] <= 1'b1;
        mm_raddr[index] <= addr;
        @(posedge mm_clk);
        mm_ren[index] <= 1'b0;
        
        // 等待有效数据
        while (!mm_rvalid[index]) @(posedge mm_clk);
        
        // 检查读取的数据
        scoreboard.check_read(channel_id, pc_id, addr, mm_rdata[index]);
    endtask

    // 监控器 - 监控所有读响应
    // always @(posedge mm_clk) begin
    //     for (int i = 0; i < NUM_PCS; i++) begin
    //         if (mm_rvalid[i]) begin
    //             scoreboard.check_read(i/2, i%2, mm_raddr[i], mm_rdata[i]);
    //         end
    //     end
    // end

    // Test scenarios
    initial begin
        scoreboard = new();

        // Initialize all control signals
        mm_wen <= '0;
        mm_ren <= '0;
        mm_waddr <= '0;
        mm_wdata <= '0;
        mm_wmask <= '0;
        mm_raddr <= '0;

        // Wait for reset
        wait(mm_rstn);
        #(CLK_PERIOD * 10);

        // Scenario 1: All PCs write simultaneously
        fork
            begin
                for (int i = 0; i < NUM_PCS; i++) begin
                    automatic int index = i;
                    fork
                        begin
                            logic [DATA_WIDTH-1:0] data;
                            logic [DATA_BYTE_WIDTH-1:0] mask;
                            data = $random();
                            mask = $random();
                            single_pc_write(index, 
                                          64'h1000 + (index << 8), 
                                          data,
                                          mask);
                        end
                    join_none
                end
                wait fork;
            end
        join

        #(CLK_PERIOD * 20);

        // Scenario 2: All PCs read simultaneously
        fork
            begin
                for (int i = 0; i < NUM_PCS; i++) begin
                    automatic int index = i;
                    fork
                        single_pc_read(index, 64'h1000 + (index << 8));
                    join_none
                end
                wait fork;
            end
        join

        #(CLK_PERIOD * 20);

        // Scenario 3: Interleaved read/write operations
        fork
            begin
                for (int i = 0; i < NUM_PCS; i++) begin
                    automatic int index = i;
                    fork
                        begin
                            if (index % 2 == 0) begin
                                single_pc_write(index,
                                              64'h2000 + (index << 8),
                                              {DATA_WIDTH{1'b1}} + index,
                                              {DATA_BYTE_WIDTH{1'b1}});
                            end else begin
                                single_pc_read(index, 64'h1000 + (index << 8));
                            end
                        end
                    join_none
                end
                wait fork;
            end
        join

        #(CLK_PERIOD * 100);
        scoreboard.report();
        $finish;
    end

    // Optional: Monitor responses
    always @(posedge mm_clk) begin
        for (int i = 0; i < NUM_PCS; i++) begin
            if (mm_rvalid[i]) begin
                $display("[TB] Channel[%0d] PC[%0d] addr[%h] = %h", i/2, i%2, mm_raddr[i], mm_rdata[i]);
            end
        end
    end

endmodule