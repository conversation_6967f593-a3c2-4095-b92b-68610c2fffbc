// fake_hbm3_pc.cc
#include "fake_hbm3_mm_bfm.h"
#include "svdpi.h"
#include <cstring>
#include <unordered_map>
#include <vector>

#ifdef NO_SIMULATOR
#define vpi_printf printf
#else
#include <vpi_user.h>
#endif

extern "C" {
    void h2s_hbm3_write(svBitVecVal* bfm_id, svBitVecVal* addr, 
                      svBitVecVal* data, int unsigned data_size,
                      svBitVecVal* mask) {
        uint64_t address = *reinterpret_cast<uint64_t*>(addr);
        FakeHBM3PC::getInstance().pcWrite(*(uint16_t*)bfm_id, address, data, data_size, mask);
    }

    void h2s_hbm3_read(svBitVecVal* bfm_id, svBitVecVal* addr,
                     svBitVecVal* rdata) {
        uint64_t address = *reinterpret_cast<uint64_t*>(addr);
        FakeHBM3PC::getInstance().pcRead(*(uint16_t*)bfm_id, address, rdata);
    }

    void h2s_hbm3_init_bfm(svBitVecVal* bfm_id, int unsigned data_width) {
        // This function is kept for compatibility with the SV BFM,
        // but it does not need to do anything in the blocking implementation.
    }
}

FakeHBM3PC& FakeHBM3PC::getInstance() {
    static FakeHBM3PC instance;
    return instance;
}

FakeHBM3PC::FakeHBM3PC() {}

FakeHBM3PC::~FakeHBM3PC() {}

void FakeHBM3PC::pcWrite(uint16_t bfm_id,
                        uint64_t addr, const svBitVecVal* data, uint32_t data_size,
                        const svBitVecVal* mask) {
    std::lock_guard<std::mutex> lock(memory_mutex_);
    MemoryKey key{bfm_id, addr};
    
    auto& mem_data = memory_[key];
    if (mem_data.empty()) {
        mem_data.resize((data_size + 7) / 8, 0);
    }

    const uint8_t* data_bytes = reinterpret_cast<const uint8_t*>(data);
    const uint8_t* mask_bytes = reinterpret_cast<const uint8_t*>(mask);
    
    for (uint32_t i = 0; i < (data_size + 7) / 8; ++i) {
        if (mask_bytes[i / 8] & (1 << (i % 8))) {
            mem_data[i] = data_bytes[i];
            vpi_printf("[pcWrite] - bfm_id: %d, addr: %lx, data: %x\n", 
                   bfm_id, addr+i, mem_data[i]);
        }
    }
}

void FakeHBM3PC::pcRead(uint16_t bfm_id,
                         uint64_t addr, svBitVecVal* rdata) {
    std::lock_guard<std::mutex> lock(memory_mutex_);
    MemoryKey key{bfm_id, addr};
    auto it = memory_.find(key);
    if (it != memory_.end()) {
        const auto& mem_data = it->second;
        memcpy(rdata, mem_data.data(), mem_data.size());
        vpi_printf("[pcRead] - bfm_id: %d, addr: %lx, data found\n", bfm_id, addr);
    } else {
        vpi_printf("[pcRead] - bfm_id: %d, addr: %lx, data not found\n", bfm_id, addr);
        // Assuming rdata points to a buffer of sufficient size (e.g., DATA_WIDTH/8)
        // If the address is not found, we return all zeros.
        memset(rdata, 0, 32); // Assuming 256-bit data width
    }
}

void FakeHBM3PC::reset() {
    std::lock_guard<std::mutex> lock(memory_mutex_);
    memory_.clear();
}
