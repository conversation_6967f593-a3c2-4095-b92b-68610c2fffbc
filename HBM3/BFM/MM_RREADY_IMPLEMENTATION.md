# HBM3 MM BFM mm_rready Implementation

## 概述
在hbm3_mm_bfm接口上增加了一个读控制信号`mm_rready`，当`mm_rready`有效时，才能返回read data。此功能在blocking和nonblock模式下都已实现。

## 修改内容

### 1. 接口信号定义
在`HBM3/BFM/src/hbm3_mm_bfm.sv`中，`mm_rready`信号已经在接口中定义：
```systemverilog
input mm_rready,
```

### 2. Nonblocking模式修改
在nonblocking模式下，修改了FIFO读取和输出控制逻辑：

**修改前：**
```systemverilog
// Output Mux: Low-latency path has priority
if (ll_rvalid_hold) begin
    mm_rdata <= ll_rdata_hold;
    mm_rvalid <= 1'b1;
end else if (!fifo_empty) begin
    // Non-blocking FIFO path is active
    mm_rdata <= read_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
    mm_rvalid <= 1'b1;
    fifo_rptr <= fifo_rptr + 1; // Only advance FIFO when its data is used
end
```

**修改后：**
```systemverilog
// Output Mux: Low-latency path has priority
// Only output data when mm_rready is asserted
if (ll_rvalid_hold && mm_rready) begin
    mm_rdata <= ll_rdata_hold;
    mm_rvalid <= 1'b1;
end else if (!fifo_empty && mm_rready) begin
    // Non-blocking FIFO path is active
    mm_rdata <= read_fifo[fifo_rptr[FIFO_AWIDTH-1:0]];
    mm_rvalid <= 1'b1;
    fifo_rptr <= fifo_rptr + 1; // Only advance FIFO when its data is used
end
```

### 3. Blocking模式修改
在blocking模式下，修改了输出控制逻辑：

**修改前：**
```systemverilog
// 输出控制
mm_rvalid <= read_valid;
if (read_valid) begin
    mm_rdata <= read_data_hold;  // use hold data
end
```

**修改后：**
```systemverilog
// 输出控制 - 只有当mm_rready有效时才输出读数据
mm_rvalid <= read_valid && mm_rready;
if (read_valid && mm_rready) begin
    mm_rdata <= read_data_hold;  // use hold data
end
```

### 4. 测试文件修改
更新了blocking和nonblocking测试文件，添加了`mm_rready`信号的声明、连接和初始化：

#### Blocking测试文件 (`HBM3/BFM/tests/blocking/src/sv/hbm3_mm_tb.sv`)
- 添加信号声明：`logic [NUM_PCS-1:0] mm_rready;`
- 添加端口连接：`.mm_rready(mm_rready[i]),`
- 添加初始化：`mm_rready = '1;  // 默认设置为高电平，允许读数据返回`

#### Nonblocking测试文件 (`HBM3/BFM/tests/nonblocking/src/sv/hbm3_mm_tb.sv`)
- 添加信号声明：`logic [NUM_PCS-1:0] mm_rready;`
- 添加端口连接：`.mm_rready(mm_rready[i]),`
- 添加初始化：`mm_rready = '1;  // 默认设置为高电平，允许读数据返回`

### 5. 新增测试文件
创建了专门的测试文件`HBM3/BFM/tests/mm_rready_test.sv`来验证`mm_rready`功能：
- 测试当`mm_rready = 0`时，`mm_rvalid`不会被断言
- 测试当`mm_rready = 1`时，读数据能正常返回
- 测试多次读取周期中的`mm_rready`控制

## 功能说明

### mm_rready信号行为
- **mm_rready = 0**: BFM不会输出读数据，`mm_rvalid`保持为0
- **mm_rready = 1**: BFM可以正常输出读数据，`mm_rvalid`根据数据可用性设置

### 两种模式下的实现差异
1. **Nonblocking模式**: 
   - 同时控制低延迟路径和FIFO路径的数据输出
   - 只有当`mm_rready`有效时才推进FIFO读指针
   
2. **Blocking模式**: 
   - 直接控制读数据的输出
   - `mm_rvalid`只有在`read_valid`和`mm_rready`都为真时才为真

## 向后兼容性
- 现有的测试文件已更新，默认将`mm_rready`设置为高电平
- 对于不使用`mm_rready`控制的应用，只需将信号连接到高电平即可保持原有行为

## 验证建议
1. 运行现有的blocking和nonblocking测试，确保基本功能不受影响
2. 运行新的`mm_rready_test.sv`测试，验证读控制功能
3. 在实际应用中测试`mm_rready`的动态控制
